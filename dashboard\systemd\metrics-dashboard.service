[Unit]
Description=Metrics Dashboard Service
Documentation=https://github.com/your-org/metrics-collector
After=network-online.target metrics-ingestion.service
Wants=network-online.target
Requires=metrics-ingestion.service
StartLimitIntervalSec=0

[Service]
Type=simple
User=metrics
Group=metrics
WorkingDirectory=/opt/metrics-collector/dashboard
Environment=PYTHONPATH=/opt/metrics-collector
Environment=PYTHONUNBUFFERED=1
ExecStart=/opt/metrics-collector/venv/bin/python app.py --host 0.0.0.0 --port 8080 --metrics-url http://localhost:8000
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal
SyslogIdentifier=metrics-dashboard

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/var/log/metrics-collector
CapabilityBoundingSet=CAP_NET_BIND_SERVICE
AmbientCapabilities=CAP_NET_BIND_SERVICE

# Resource limits
LimitNOFILE=65536
MemoryMax=512M
CPUQuota=50%

[Install]
WantedBy=multi-user.target
