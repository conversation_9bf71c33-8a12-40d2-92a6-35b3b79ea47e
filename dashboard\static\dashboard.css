/* Dashboard Styles */

body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid #e9ecef;
    font-weight: 600;
    color: #495057;
}

.navbar-brand {
    font-weight: 600;
    font-size: 1.5rem;
}

/* Status Cards */
.card.bg-primary {
    background: linear-gradient(135deg, #007bff, #0056b3) !important;
}

.card.bg-info {
    background: linear-gradient(135deg, #17a2b8, #117a8b) !important;
}

.card.bg-warning {
    background: linear-gradient(135deg, #ffc107, #d39e00) !important;
}

.card.bg-success {
    background: linear-gradient(135deg, #28a745, #1e7e34) !important;
}

/* Chart containers */
.card-body canvas {
    max-height: 300px;
}

/* Loading and error states */
#loadingIndicator {
    padding: 2rem;
}

.spinner-border {
    width: 3rem;
    height: 3rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .container-fluid {
        padding: 0 10px;
    }
    
    .card-body canvas {
        max-height: 250px;
    }
    
    .navbar .d-flex {
        flex-direction: column;
        gap: 10px;
    }
    
    .navbar .d-flex > * {
        width: 100% !important;
    }
}

/* Custom form controls */
.form-select {
    border-radius: 6px;
    border: 1px solid #ced4da;
}

.btn-outline-light:hover {
    background-color: #f8f9fa;
    color: #212529;
}

/* Alert styling */
.alert {
    border-radius: 8px;
    border: none;
}

/* Animation for status updates */
.status-update {
    animation: pulse 0.5s ease-in-out;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Chart legend styling */
.chart-legend {
    display: flex;
    justify-content: center;
    margin-top: 10px;
}

.chart-legend-item {
    display: flex;
    align-items: center;
    margin: 0 10px;
    font-size: 0.9rem;
}

.chart-legend-color {
    width: 12px;
    height: 12px;
    border-radius: 2px;
    margin-right: 5px;
}
